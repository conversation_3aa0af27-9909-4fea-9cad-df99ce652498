import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateMultipleImagesSupport1************
  implements MigrationInterface
{
  name = 'UpdateMultipleImagesSupport1************';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('Starting multiple images support migration...');

    // ========================================
    // CREATE JUNCTION TABLES
    // ========================================

    console.log('Creating junction tables for N:M relationships...');

    // Create image_edit_original_image_completion junction table
    await queryRunner.query(`
      CREATE TABLE "image_edit_original_image_completion" (
        "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
        "image_edit_id" uuid NOT NULL,
        "image_completion_id" uuid NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "FK_image_edit_original_image_completion_image_edit"
          FOREIGN KEY ("image_edit_id") REFERENCES "image_edit"("id") ON DELETE CASCADE,
        CONSTRAINT "FK_image_edit_original_image_completion_image_completion"
          FOREIGN KEY ("image_completion_id") REFERENCES "image_completion"("id") ON DELETE CASCADE
      )
    `);

    // Create unique index to prevent duplicates
    await queryRunner.query(`
      CREATE UNIQUE INDEX "idx_image_edit_original_image_completion_unique"
      ON "image_edit_original_image_completion" ("image_edit_id", "image_completion_id")
    `);

    // Create indexes for efficient queries
    await queryRunner.query(`
      CREATE INDEX "idx_image_edit_original_image_completion_image_edit_id"
      ON "image_edit_original_image_completion" ("image_edit_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_image_edit_original_image_completion_image_completion_id"
      ON "image_edit_original_image_completion" ("image_completion_id")
    `);

    // Create image_completion_original_image_completion junction table
    await queryRunner.query(`
      CREATE TABLE "image_completion_original_image_completion" (
        "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
        "image_completion_id" uuid NOT NULL,
        "original_image_completion_id" uuid NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "FK_image_completion_original_image_completion_image_completion"
          FOREIGN KEY ("image_completion_id") REFERENCES "image_completion"("id") ON DELETE CASCADE,
        CONSTRAINT "FK_image_completion_original_image_completion_original"
          FOREIGN KEY ("original_image_completion_id") REFERENCES "image_completion"("id") ON DELETE CASCADE
      )
    `);

    // Create unique index to prevent duplicates
    await queryRunner.query(`
      CREATE UNIQUE INDEX "idx_image_completion_original_image_completion_unique"
      ON "image_completion_original_image_completion" ("image_completion_id", "original_image_completion_id")
    `);

    // Create indexes for efficient queries
    await queryRunner.query(`
      CREATE INDEX "idx_image_completion_original_image_completion_image_completion_id"
      ON "image_completion_original_image_completion" ("image_completion_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_image_completion_original_image_completion_original_id"
      ON "image_completion_original_image_completion" ("original_image_completion_id")
    `);

    // ========================================
    // MIGRATE EXISTING DATA
    // ========================================

    console.log('Migrating existing data to junction tables...');

    // Migrate image_edit data
    await queryRunner.query(`
      INSERT INTO "image_edit_original_image_completion" ("image_edit_id", "image_completion_id")
      SELECT "id", "original_image_completion_id"
      FROM "image_edit"
      WHERE "original_image_completion_id" IS NOT NULL
    `);

    // Migrate image_completion data
    await queryRunner.query(`
      INSERT INTO "image_completion_original_image_completion" ("image_completion_id", "original_image_completion_id")
      SELECT "id", "original_image_completion_id"
      FROM "image_completion"
      WHERE "original_image_completion_id" IS NOT NULL
    `);

    // ========================================
    // UPDATE EXISTING TABLES
    // ========================================

    console.log('Updating existing tables...');

    // Drop old foreign key constraints and indexes
    await queryRunner.query(`
      ALTER TABLE "image_edit" DROP CONSTRAINT "FK_b45b48fc973cbea3f3805ccee86"
    `);

    await queryRunner.query(`
      DROP INDEX "public"."idx_image_edit_original_image_completion_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "image_completion" DROP CONSTRAINT "FK_b2c9823ad4fc3641d61fdcdf9c6"
    `);

    await queryRunner.query(`
      DROP INDEX "public"."idx_image_completion_original_image_completion_id"
    `);

    // Drop old columns (they're now replaced by junction tables)
    await queryRunner.query(`
      ALTER TABLE "image_edit" DROP COLUMN "original_image_completion_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "image_completion" DROP COLUMN "original_image_completion_id"
    `);

    // Update input_image_url to input_image_urls (JSON array) for image_edit
    await queryRunner.query(`
      ALTER TABLE "image_edit"
      ALTER COLUMN "input_image_url" TYPE json USING
      CASE
        WHEN "input_image_url" IS NULL THEN NULL
        ELSE json_build_array("input_image_url")
      END
    `);

    await queryRunner.query(`
      ALTER TABLE "image_edit"
      RENAME COLUMN "input_image_url" TO "input_image_urls"
    `);

    console.log('Multiple images support migration completed successfully!');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('Rolling back multiple images support migration...');

    // ========================================
    // RESTORE ORIGINAL COLUMNS
    // ========================================

    console.log('Restoring original columns...');

    // Add back original_image_completion_id columns
    await queryRunner.query(`
      ALTER TABLE "image_edit" ADD COLUMN "original_image_completion_id" uuid
    `);

    await queryRunner.query(`
      ALTER TABLE "image_completion" ADD COLUMN "original_image_completion_id" uuid
    `);

    // ========================================
    // MIGRATE DATA BACK FROM JUNCTION TABLES
    // ========================================

    console.log('Migrating data back from junction tables...');

    // Migrate image_edit data back (taking first relationship)
    await queryRunner.query(`
      UPDATE "image_edit"
      SET "original_image_completion_id" = subquery.image_completion_id
      FROM (
        SELECT DISTINCT ON (image_edit_id) image_edit_id, image_completion_id
        FROM "image_edit_original_image_completion"
        ORDER BY image_edit_id, created_at ASC
      ) AS subquery
      WHERE "image_edit"."id" = subquery.image_edit_id
    `);

    // Migrate image_completion data back (taking first relationship)
    await queryRunner.query(`
      UPDATE "image_completion"
      SET "original_image_completion_id" = subquery.original_image_completion_id
      FROM (
        SELECT DISTINCT ON (image_completion_id) image_completion_id, original_image_completion_id
        FROM "image_completion_original_image_completion"
        ORDER BY image_completion_id, created_at ASC
      ) AS subquery
      WHERE "image_completion"."id" = subquery.image_completion_id
    `);

    // ========================================
    // RESTORE ORIGINAL CONSTRAINTS AND INDEXES
    // ========================================

    console.log('Restoring original constraints and indexes...');

    // Recreate original indexes
    await queryRunner.query(`
      CREATE INDEX "idx_image_edit_original_image_completion_id" ON "image_edit" ("original_image_completion_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_image_completion_original_image_completion_id" ON "image_completion" ("original_image_completion_id")
    `);

    // Recreate foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "image_edit"
      ADD CONSTRAINT "FK_b45b48fc973cbea3f3805ccee86" FOREIGN KEY ("original_image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "image_completion"
      ADD CONSTRAINT "FK_b2c9823ad4fc3641d61fdcdf9c6" FOREIGN KEY ("original_image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    // ========================================
    // REVERT INPUT IMAGE URLS
    // ========================================

    console.log('Reverting input image URLs...');

    // Convert input_image_urls back to input_image_url (taking first element)
    await queryRunner.query(`
      ALTER TABLE "image_edit"
      ALTER COLUMN "input_image_urls" TYPE text USING
      CASE
        WHEN "input_image_urls" IS NULL THEN NULL
        WHEN json_array_length("input_image_urls") > 0 THEN
          ("input_image_urls"->0)::text
        ELSE NULL
      END
    `);

    await queryRunner.query(`
      ALTER TABLE "image_edit"
      RENAME COLUMN "input_image_urls" TO "input_image_url"
    `);

    // ========================================
    // DROP JUNCTION TABLES
    // ========================================

    console.log('Dropping junction tables...');

    await queryRunner.query(
      `DROP TABLE "image_edit_original_image_completion"`,
    );
    await queryRunner.query(
      `DROP TABLE "image_completion_original_image_completion"`,
    );

    console.log('Multiple images support migration rollback completed!');
  }
}
