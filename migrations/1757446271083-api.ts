import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1757446271083 implements MigrationInterface {
  name = 'Api1757446271083';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "upscale"
            ADD "size" integer
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "upscale" DROP COLUMN "size"
        `);
  }
}
