import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1758029746490 implements MigrationInterface {
  name = 'Api1758029746490';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TYPE "public"."upscale_mode_enum" AS ENUM(
                'natural',
                'sharp',
                'default',
                'legacy',
                'creative'
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "upscale"
            ADD "mode" "public"."upscale_mode_enum" NOT NULL DEFAULT 'default'
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "upscale" DROP COLUMN "mode"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."upscale_mode_enum"
        `);
  }
}
