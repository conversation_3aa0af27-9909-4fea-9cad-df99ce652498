import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ImageCompletionEntity } from '../../image-completion/entity/image-completion.entity';
import { OrganizationEntity } from '../../organization/entity/organization.entity';
import { UserEntity } from '../../user/entity/user.entity';

export enum StatusEnum {
  NEW = 'new',
  GENERATING = 'generating',
  READY = 'ready',
  FAILED = 'failed',
}

export enum ModeEnum {
  NATURAL = 'natural',
  SHARP = 'sharp',
  DEFAULT = 'default',
  LEGACY = 'legacy',
  CREATIVE = 'creative',
}

@Entity('upscale')
export class UpscaleEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_upscale_user_id')
  userId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_upscale_organization_id')
  organizationId: string;

  @ManyToOne(() => OrganizationEntity)
  organization: OrganizationEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_upscale_image_completion_id')
  imageCompletionId: string;

  @ManyToOne(() => ImageCompletionEntity, { lazy: true })
  imageCompletion: ImageCompletionEntity;

  @Column({ type: 'text', nullable: true })
  imageUrl?: string;

  @Column({ type: 'text', nullable: true })
  prompt?: string;

  @Column({ type: 'text', nullable: true })
  promptSystem?: string;

  @Column({ nullable: false, default: 'new' })
  status: string;

  @Column({ nullable: true })
  size?: number;

  @Column({
    type: 'enum',
    enum: ModeEnum,
    nullable: false,
    default: ModeEnum.DEFAULT,
  })
  mode: ModeEnum;

  @Column({ type: 'text', nullable: true })
  statusDetail?: string;

  @Column({ nullable: false, default: 0 })
  progress: number;

  @Column({ type: 'text', nullable: true })
  storageBucket?: string;

  @Column({ type: 'text', nullable: true })
  storagePath?: string;

  @Column({ type: 'text', nullable: true })
  imagePath?: string;

  @Column({ type: 'json', nullable: true, default: {} })
  generationSettings?: any;

  @Column({ type: 'text', nullable: true })
  generationData?: string;

  @Column({ nullable: true })
  generatedByUnit: string;

  @Column({ nullable: false, default: 0 })
  generationSeconds: number;

  @Column({ default: false })
  isSelected: boolean;

  @Column({ default: false })
  isActive: boolean;

  @Column({ type: 'text', nullable: true })
  webhookUrl?: string;

  @Column({ type: 'timestamp', nullable: true })
  blockedAt?: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
