import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';
import { PaymentRequiredException } from 'src/subscription/exception/payment-required.exception';
import { UserEntity } from '../../user/entity/user.entity';
import { UpscaleInternalRequest } from '../dto/upscale.internal-request';
import { UpscaleRequest } from '../dto/upscale.request';
import { UpscaleEntity } from '../entity/upscale.entity';
import { UpscaleManager } from './manager';

@Injectable()
export class UpscaleRequestManager {
  constructor(
    private manager: UpscaleManager,
    private imageCompletionProvider: ImageCompletionProvider,
    private readonly organizationUserProvider: OrganizationUserProvider,
  ) {}

  async create(
    request: UpscaleRequest,
    user: UserEntity,
  ): Promise<UpscaleEntity> {
    const entity = new UpscaleEntity();

    let imageCompletion = null;

    if ('imageCompletionId' in request) {
      imageCompletion = await this.imageCompletionProvider.get(
        request.imageCompletionId,
      );

      entity.imageCompletion = imageCompletion;
      entity.imageCompletionId = imageCompletion.id;
    }

    if ('imageUrl' in request) {
      entity.imageUrl = request.imageUrl;
    }

    entity.user = user;
    entity.prompt = request.prompt;
    entity.webhookUrl = request.webhookUrl;
    entity.imageUrl = request.imageUrl;
    entity.size = request.size;
    entity.mode = request.mode;

    entity.generationSettings = {
      strength: request.strength,
    };

    if (request.organizationId) {
      if (
        !(await this.organizationUserProvider.isMember(
          user.id,
          request.organizationId,
        ))
      ) {
        throw new UnauthorizedException();
      }
      entity.organizationId = request.organizationId;
    }

    try {
      await this.manager.create(entity);

      return entity;
    } catch (e) {
      if ('user_credit_balance.not_found' === e.message) {
        throw new PaymentRequiredException();
      }

      throw new BadRequestException(e.message);
    }
  }

  async updateInternal(
    entity: UpscaleEntity,
    request: UpscaleInternalRequest,
  ): Promise<UpscaleEntity> {
    this.mapInternalRequestData(entity, request);

    return await this.manager.update(entity);
  }

  mapInternalRequestData(
    entity: UpscaleEntity,
    request: UpscaleInternalRequest,
  ): void {
    entity.webhookUrl = request.webhookUrl ?? entity.webhookUrl;
    entity.prompt = request.prompt ?? entity.prompt;
    entity.promptSystem = request.promptSystem ?? entity.promptSystem;
    entity.status = request.status ?? entity.status;
    entity.mode = request.mode ?? entity.mode;
    entity.progress = request.progress ?? entity.progress;
    entity.statusDetail = request.statusDetail ?? entity.statusDetail;
    entity.imagePath = request.imagePath ?? entity.imagePath;
    entity.generationSettings =
      request.generationSettings ?? entity.generationSettings;
    entity.generationData = request.generationData ?? entity.generationData;
    entity.generatedByUnit = request.generatedByUnit ?? entity.generatedByUnit;
    entity.generationSeconds =
      request.generationSeconds ?? entity.generationSeconds;
    entity.isActive = request.isActive ?? entity.isActive;
  }
}
