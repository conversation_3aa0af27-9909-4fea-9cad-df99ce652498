import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserProvider } from 'src/user/service/provider';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { UpscaleDto } from '../dto/upscale.dto';
import { UpscaleEntity } from '../entity/upscale.entity';

@Injectable()
export class UpscaleResponseMapper {
  private cdnHost: string;

  constructor(
    private configService: ConfigService,
    private userResponseMapper: UserResponseMapper,
    private userProvider: UserProvider,
  ) {
    this.cdnHost = this.configService.get<string>('CDN_HOST');
  }

  async mapMultiple(entities: UpscaleEntity[]): Promise<any> {
    const images = [];

    for (const entity of entities) {
      try {
        images.push(await this.map(entity, true));
      } catch (error) {
        console.error({
          message: 'Error mapping image response',
          upscale_id: entity.id,
          error: error.message,
        });
      }
    }

    return images;
  }

  async mapImage(entity: UpscaleEntity): Promise<UpscaleDto> {
    const user = entity.user ?? (await this.userProvider.get(entity.userId));

    const dto = new UpscaleDto();
    dto.generationSettings = entity.generationSettings;
    dto.selected = entity.isSelected;
    dto.createdByUsername = user.username;
    dto.createdByThumbnail = this.userResponseMapper.mapProfilePicture(user);
    dto.prompt = entity.prompt;
    dto.id = entity.id;
    dto.createdAt = entity.createdAt;
    if (entity.imagePath) {
      dto.imageVersions = this.generateImageVersions(entity.imagePath);
    }

    return dto;
  }

  async map(entity: UpscaleEntity, mapUrls = true): Promise<UpscaleDto> {
    const dto = new UpscaleDto();

    dto.id = entity.id;
    if (entity.user) {
      dto.user = this.userResponseMapper.mapPublic(entity.user);
      dto.username = entity.user.username;
      dto.isUserVerified = entity.user.isVerified;
    }
    dto.imageCompletionId = entity.imageCompletionId;
    dto.imageUrl = entity.imageUrl;
    dto.prompt = entity.prompt;
    dto.status = entity.status;
    dto.statusDetail = entity.statusDetail;
    dto.progress = entity.progress;
    dto.generationSettings = entity.generationSettings;
    dto.webhookUrl = entity.webhookUrl;
    dto.size = entity.size;
    dto.blockedAt = entity.blockedAt;
    dto.createdAt = entity.createdAt;
    dto.mode = entity.mode;

    if (mapUrls && entity.imagePath) {
      dto.imageVersions = this.generateImageVersions(entity.imagePath);
    }

    if (entity.imageCompletionId) {
      const imageCompletion = await entity.imageCompletion;

      dto.originalImageThumbnail = this.generateImageVersions(
        imageCompletion.imagePaths[0],
      )['96x96'];
    }

    return dto;
  }

  generateImageVersions(originalImage: string): {
    [key: string]: string;
  } {
    const originalImageUrl = this.cdnHost + '/' + originalImage;

    const versions = {
      original: originalImageUrl,
      '96x96': this.composeUrlWithResolution(originalImageUrl, '96x96'),
      '240x240': this.composeUrlWithResolution(originalImageUrl, '240x240'),
      '640x640': this.composeUrlWithResolution(originalImageUrl, '640x640'),
      '1920x1920': this.composeUrlWithResolution(originalImageUrl, '1920x1920'),
    };

    return versions;
  }

  private composeUrlWithResolution(
    originalImageUrl: string,
    resolution: string,
  ): string {
    const [base, extension] = originalImageUrl.split(/\.(?=[^\.]+$)/);
    return `${base}_${resolution}.${extension}`;
  }

  async mapMultipleInternal(entities: UpscaleEntity[]): Promise<any> {
    const images = [];

    for (const entity of entities) {
      images.push(await this.mapInternal(entity));
    }

    return images;
  }

  async mapInternal(entity: UpscaleEntity): Promise<UpscaleDto> {
    const dto = await this.map(entity, true);

    dto.userId = entity.user.id;
    dto.promptSystem = entity.promptSystem;
    dto.storageBucket = entity.storageBucket;
    dto.storagePath = entity.storagePath;
    dto.generationSettings = entity.generationSettings;
    dto.generationData = entity.generationData;
    dto.generatedByUnit = entity.generatedByUnit;
    dto.generationSeconds = entity.generationSeconds;

    return dto;
  }
}
