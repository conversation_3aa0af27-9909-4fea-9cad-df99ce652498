import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUrl,
  IsU<PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  ValidateIf,
} from 'class-validator';
import { ModeEnum } from '../entity/upscale.entity';

export class UpscaleRequest {
  @ApiProperty()
  @IsString()
  @IsOptional()
  prompt: string;

  @ApiProperty()
  @IsEnum(ModeEnum)
  @IsOptional()
  mode: ModeEnum = ModeEnum.DEFAULT;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  @ValidateIf((o) => !o.imageUrl)
  imageCompletionId: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  organizationId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @ValidateIf((o) => !o.imageCompletionId)
  imageUrl: string;

  @ApiProperty({ default: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1, { message: 'Strength must be at least 1' })
  @Max(5, { message: 'Strength must be at most 5' })
  strength = 1;

  @ApiProperty()
  @IsOptional()
  @IsUrl()
  webhookUrl?: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Min(2, { message: 'Size must be at least 2' })
  @Max(12, { message: 'Size must be at most 12' })
  size?: number;
}
