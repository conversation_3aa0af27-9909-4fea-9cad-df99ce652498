import {
  <PERSON>ti<PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyTo<PERSON>ne,
  JoinColumn,
  CreateDateColumn,
  Index,
} from 'typeorm';
import { ImageCompletionEntity } from './image-completion.entity';

/**
 * Junction table for the many-to-many relationship between ImageCompletion and its original ImageCompletions
 * This represents the original image completions used as source for a new image completion
 */
@Entity('image_completion_original_image_completion')
@Index(['imageCompletionId', 'originalImageCompletionId'], { unique: true })
@Index(['imageCompletionId'])
@Index(['originalImageCompletionId'])
export class ImageCompletionOriginalImageCompletionEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => ImageCompletionEntity, (imageCompletion) => imageCompletion.originalImageCompletionRelations, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'image_completion_id' })
  imageCompletion: ImageCompletionEntity;

  @Column({ name: 'image_completion_id', type: 'uuid' })
  imageCompletionId: string;

  @ManyToOne(() => ImageCompletionEntity, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'original_image_completion_id' })
  originalImageCompletion: ImageCompletionEntity;

  @Column({ name: 'original_image_completion_id', type: 'uuid' })
  originalImageCompletionId: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}
