import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';
import { PaymentRequiredException } from 'src/subscription/exception/payment-required.exception';
import { UserProvider } from 'src/user/service/provider';
import {
  ModelEntity,
  PrivacyEnum as ModelPrivacyEnum,
} from '../../model/entity/model.entity';
import { UserEntity } from '../../user/entity/user.entity';
import { ImageCompletionPrivacyRequest } from '../dto/image-completion-privacy.request';
import { ImageCompletionInternalRequest } from '../dto/image-completion.internal-request';
import { ImageCompletionRequest } from '../dto/image-completion.request';
import { ImageCompletionSearchRequest } from '../dto/image-completion.search-request';
import {
  ImageCompletionEntity,
  PrivacyEnum as ImageCompletionPrivacyEnum,
  PrivacyEnum,
  StatusEnum,
} from '../entity/image-completion.entity';
import { ImageCompletionManager } from './manager';
import { ImageCompletionProvider } from './provider';
import { ImageCompletionPromptPrivacyRequest } from '../dto/image-completion-prompt-privacy.request';

@Injectable()
export class ImageCompletionRequestManager {
  constructor(
    private readonly manager: ImageCompletionManager,
    private readonly provider: ImageCompletionProvider,
    private readonly userProvider: UserProvider,
    private readonly organizationUserProvider: OrganizationUserProvider,
    private readonly logger: Logger,
  ) {}

  async create(
    request: ImageCompletionRequest,
    user: UserEntity,
  ): Promise<ImageCompletionEntity> {
    if (!request.prompt && !request.imageCompletionId) {
      throw new BadRequestException(
        'Either prompt or imageCompletionId must be provided.',
      );
    }

    const entity = new ImageCompletionEntity();

    let imageCompletion = null;

    if (request.imageCompletionId) {
      imageCompletion = await this.provider.get(request.imageCompletionId);

      request.prompt = imageCompletion.prompt;
      entity.regeneratedFrom = imageCompletion;

      if (!imageCompletion.regenerations) {
        imageCompletion.regenerations = 0;
      }
      imageCompletion.regenerations++;
    }

    entity.privacy = ImageCompletionPrivacyEnum.PRIVATE;
    entity.user = user;
    entity.prompt = request.prompt;
    entity.queue = request.queue;
    entity.systemVersion = request.systemVersion ?? user.systemVersion;
    entity.generationSettings = request.generationSettings ?? {};
    entity.hasWatermark = request.hasWatermark ?? entity.hasWatermark;
    entity.webhookUrl = request.webhookUrl ?? entity.webhookUrl;

    if (user.hidePrompt) {
      entity.hidePrompt = user.hidePrompt;
    }

    await this.manager.generateImageCompletionModels(entity);

    if (request.organizationId) {
      if (
        !(await this.organizationUserProvider.isMember(
          user.id,
          request.organizationId,
        ))
      ) {
        throw new UnauthorizedException();
      }
      entity.organizationId = request.organizationId;
    }

    try {
      await this.manager.create(entity);

      if (null !== imageCompletion) {
        await this.manager.update(imageCompletion);
      }

      return entity;
    } catch (e) {
      if ('user_credit_balance.not_found' === e.message) {
        throw new PaymentRequiredException();
      }

      this.logger.error('Error creating image completion', {
        imageCompletion: entity,
        error: e.message,
      });

      throw new BadRequestException(e.message);
    }
  }

  async updatePromptPrivacy(
    entity: ImageCompletionEntity,
    request: ImageCompletionPromptPrivacyRequest,
  ): Promise<ImageCompletionEntity> {
    entity.hidePrompt = request.hidePrompt;
    return await this.manager.update(entity);
  }

  async interrupt(entity: ImageCompletionEntity) {
    if (
      entity.status != StatusEnum.GENERATING &&
      entity.status != StatusEnum.NEW
    ) {
      throw new BadRequestException('Image is not being generated');
    }

    entity.status = StatusEnum.INTERRUPTED;
    entity.publishedAt = null;

    try {
      await this.manager.save(entity);
    } catch (e) {
      if ('user_credit_balance.not_found' === e.message) {
        throw new PaymentRequiredException();
      }

      this.logger.error('Error interrupting image generation', {
        imageCompletion: entity,
        error: e.message,
      });

      throw new BadRequestException(e.message);
    }
  }

  async updatePrivacy(
    entity: ImageCompletionEntity,
    request: ImageCompletionPrivacyRequest,
  ): Promise<ImageCompletionEntity> {
    if (entity.status != StatusEnum.READY) {
      throw new BadRequestException('Image not ready for publication');
    }

    if (entity.isNsfw || entity.hasPrivateModel) {
      throw new BadRequestException('Image publication not allowed');
    }

    entity.privacy = request.privacy;

    return await this.manager.update(entity);
  }

  async updateInternal(
    entity: ImageCompletionEntity,
    request: ImageCompletionInternalRequest,
  ): Promise<ImageCompletionEntity> {
    this.mapInternalRequestData(entity, request);

    if (entity.status !== StatusEnum.GENERATING) {
      entity.previewImage = null;
    }

    try {
      return await this.manager.update(entity);
    } catch (e) {
      if ('user_credit_balance.not_found' === e.message) {
        throw new PaymentRequiredException();
      }

      this.logger.error('Error updating image', {
        imageCompletion: entity,
        error: e.message,
      });

      throw new BadRequestException(e.message);
    }
  }

  async markAsResized(entity: ImageCompletionEntity) {
    entity.isResized = true;

    await this.manager.save(entity);
  }

  extractModelNamesFromPrompt(prompt: string): string[] {
    return (prompt.match(/@(\w+)/g) || []).map((name) => name.slice(1));
  }

  extractHighestPrivacy(models: ModelEntity[]): string {
    const privacyLevels = models.map((model) => model.privacy);
    if (privacyLevels.includes(ModelPrivacyEnum.LICENSED)) {
      return ModelPrivacyEnum.LICENSED;
    }
    if (privacyLevels.includes(ModelPrivacyEnum.PRIVATE)) {
      return ModelPrivacyEnum.PRIVATE;
    }
    return ModelPrivacyEnum.PUBLIC;
  }

  mapInternalRequestData(
    entity: ImageCompletionEntity,
    request: ImageCompletionInternalRequest,
  ): void {
    entity.prompt = request.prompt ?? entity.prompt;
    entity.promptSystem = request.promptSystem ?? entity.promptSystem;
    entity.baseModel = request.baseModel ?? entity.baseModel;
    entity.privacy = request.privacy ?? entity.privacy;
    entity.status = request.status ?? entity.status;
    entity.progress = request.progress ?? entity.progress;
    entity.previewImage = request.previewImage ?? entity.previewImage;
    entity.likes = request.likes ?? entity.likes;
    entity.reports = request.reports ?? entity.reports;
    entity.statusDetail = request.statusDetail ?? entity.statusDetail;
    entity.imagePaths = request.imagePaths ?? entity.imagePaths;
    entity.generationSettings =
      request.generationSettings ?? entity.generationSettings;
    entity.hasWatermark = request.hasWatermark ?? entity.hasWatermark;
    entity.generationData = request.generationData ?? entity.generationData;
    entity.generatedByUnit = request.generatedByUnit ?? entity.generatedByUnit;
    entity.generationSeconds =
      request.generationSeconds ?? entity.generationSeconds;
    entity.isNsfw = request.isNsfw ?? entity.isNsfw;
    entity.isUnsafe = request.isUnsafe ?? entity.isUnsafe;
    entity.isHot = request.isHot ?? entity.isHot;
    entity.isActive = request.isActive ?? entity.isActive;
    entity.isResized = request.isResized ?? entity.isResized;
  }

  async sanitizeSearchFilters(
    filters: ImageCompletionSearchRequest,
    currentUser?: UserEntity,
  ): Promise<any> {
    const hasUserFilter =
      currentUser &&
      ((filters.hasOwnProperty('userId') &&
        filters.userId !== currentUser.id) ||
        (filters.hasOwnProperty('username') &&
          filters.username !== currentUser.username));

    if (hasUserFilter) {
      // If the privacy filter is set to private and the user is not the owner, throw an exception
      if (
        filters.hasOwnProperty('privacy') &&
        filters.privacy !== PrivacyEnum.PUBLIC
      ) {
        throw new UnauthorizedException(
          "You are not allowed to view other users' private models.",
        );
      }

      // if an user filter is set, and it's different than the current user, then allow for only public images
      filters.privacy = PrivacyEnum.PUBLIC;
      filters.status = StatusEnum.READY;
    }

    if (filters.hasOwnProperty('username')) {
      const filteredUser = await this.userProvider.getBy({
        username: filters.username,
      });

      filters.userId = filteredUser.id;

      delete filters.username;
    }

    // if privacy is set, then we can only show the current user's images
    if (
      filters.hasOwnProperty('privacy') &&
      PrivacyEnum.PUBLIC !== filters.privacy
    ) {
      filters.userId = currentUser.id;
    }

    const criteria = {
      followerId: null,
      currentUserId: null,
      hideFromUserProfile: false,
      ...filters,
    };

    if (!filters.hasOwnProperty('privacy') && !hasUserFilter) {
      criteria.currentUserId = currentUser?.id;
    }

    if (filters.onlyFollowing) {
      criteria.followerId = currentUser?.id;
    }

    return criteria;
  }
}
