import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Logger } from 'nestjs-pino';
import { ImageEditResponseMapper } from 'src/image-edit/service/response-mapper';
import { ModelProvider } from 'src/model/service/provider';
import { ModelResponseMapper } from 'src/model/service/response-mapper';
import { UpscaleResponseMapper } from 'src/upscale/service/response-mapper';
import { UserResponseMapper } from 'src/user/service/response-mapper';
import { BookmarkProvider } from '../../bookmark/service/bookmark.provider';
import { UserProvider } from '../../user/service/provider';
import { ImageCompletionDto } from '../dto/image-completion.dto';
import { ImageCompletionEntity } from '../entity/image-completion.entity';
import { ImageCompletionLikeProvider } from './like.provider';
import { ImageCompletionProvider } from './provider';

@Injectable()
export class ImageCompletionResponseMapper {
  private cdnHost: string;

  constructor(
    private readonly modelResponseMapper: ModelResponseMapper,
    private readonly configService: ConfigService,
    private readonly likeProvider: ImageCompletionLikeProvider,
    private readonly bookmarkProvider: BookmarkProvider,
    private readonly modelProvider: ModelProvider,
    private readonly userProvider: UserProvider,
    private readonly userResponseMapper: UserResponseMapper,
    private readonly upscaleResponseMapper: UpscaleResponseMapper,
    @Inject(forwardRef(() => ImageEditResponseMapper))
    private readonly imageEditResponseMapper: ImageEditResponseMapper,
    private readonly imageCompletionProvider: ImageCompletionProvider,
    private readonly logger: Logger,
  ) {
    this.cdnHost = this.configService.get<string>('CDN_HOST');
  }

  async mapMultiple(
    entities: ImageCompletionEntity[],
    userId: string = null,
  ): Promise<ImageCompletionDto[]> {
    const images = [];

    for (const entity of entities) {
      try {
        images.push(await this.map(entity, true, true, userId));
      } catch (error) {
        this.logger.debug('Error mapping image response', {
          image_completion_id: entity?.id,
          error: error.message,
        });
      }
    }

    return images;
  }

  async map(
    entity: ImageCompletionEntity,
    mapUrls = true,
    mapModels = true,
    userId: string = null,
    mapEdit = true,
  ): Promise<ImageCompletionDto> {
    const dto = new ImageCompletionDto();

    const user = entity.user ?? (await this.userProvider.get(entity.userId));

    dto.id = entity.id;
    dto.user = this.userResponseMapper.mapPublic(user);
    dto.regeneratedFromId = entity.regeneratedFromId;
    if (!entity.hidePrompt) {
      dto.prompt = entity.prompt;
    }
    dto.status = entity.status;
    dto.statusDetail = entity.statusDetail;
    dto.progress = entity.progress;
    dto.previewImage = entity.previewImage;
    dto.username = user.username;
    dto.queue = entity.queue;
    dto.isUserVerified = user.isVerified;
    dto.privacy = entity.privacy;
    dto.likes = entity.likes;
    dto.comments = entity.comments;
    dto.regenerations = entity.regenerations;
    dto.generationSettings = entity.generationSettings;
    dto.systemVersion = entity.systemVersion;
    dto.reports = entity.reports;
    dto.hasWatermark = entity.hasWatermark;
    dto.isNsfw = entity.isNsfw;
    dto.webhookUrl = entity.webhookUrl;
    dto.isUnsafe = entity.isUnsafe;
    dto.isHot = entity.isHot;
    dto.hidePrompt = entity.hidePrompt;
    dto.blockedAt = entity.blockedAt;
    dto.createdAt = entity.createdAt;

    if (
      entity.originalImageCompletionRelations &&
      entity.originalImageCompletionRelations.length > 0
    ) {
      try {
        // Load all original image completions
        const originalImageCompletions = await Promise.all(
          entity.originalImageCompletionRelations.map((relation) =>
            this.imageCompletionProvider.get(
              relation.originalImageCompletionId,
            ),
          ),
        );

        // Map all original image completions
        const mappedOriginalImageCompletions = await Promise.all(
          originalImageCompletions.map((originalImageCompletion) =>
            this.map(
              originalImageCompletion,
              mapUrls,
              mapModels,
              userId,
              false,
            ),
          ),
        );

        // Set all original image completions
        dto.originalImageCompletions = mappedOriginalImageCompletions;
      } catch (error) {
        this.logger.debug(
          'Error mapping imageCompletion.originalImageCompletions',
          {
            image_completion_id: entity.id,
            original_image_completion_ids:
              entity.originalImageCompletionRelations?.map(
                (r) => r.originalImageCompletionId,
              ),
            error: error.message,
          },
        );
      }
    }

    // Add the array property to the DTO (ensure it's never undefined)
    dto.originalImageCompletionIds =
      entity.originalImageCompletionRelations?.map(
        (r) => r.originalImageCompletionId,
      ) || [];

    // Ensure originalImageCompletions is always an array (even if empty)
    if (!dto.originalImageCompletions) {
      dto.originalImageCompletions = [];
    }

    if (mapEdit && entity.imageEditImageCompletion) {
      try {
        dto.imageEdit = await this.imageEditResponseMapper.map(
          entity.imageEditImageCompletion.imageEdit,
          false,
          false,
        );
      } catch (error) {
        this.logger.debug('Error mapping image edit', {
          image_completion: entity,
          error: error.message,
        });
      }
    }

    if (userId) {
      const userHasLiked = await this.likeProvider.countBy({
        imageCompletionId: entity.id,
        userId: userId,
      });

      dto.liked = userHasLiked > 0;

      dto.isBookmarked = await this.bookmarkProvider.isBookmarked(
        entity.id,
        'image',
        userId,
      );
    }

    if (mapUrls && entity.imagePaths && entity.imagePaths.length > 0) {
      // dto.imagePaths = [];

      // for (const imagePath of entity.imagePaths) {
      //   dto.imagePaths.push(
      //     await this.assetManager.generateSignedUrl(entity, imagePath, 60 * 60), // URL expires in 60 minutes
      //   );
      // }

      dto.imageVersions = this.generateImageVersions(entity.imagePaths[0]);

      if (entity.upscales && entity.upscales.length > 0) {
        dto.imageVersions.upscale = [];

        for (const upscale of entity.upscales) {
          if (upscale.status != 'ready') {
            continue;
          }

          dto.imageVersions.upscale.push(
            await this.upscaleResponseMapper.mapImage(upscale),
          );

          dto.imageVersions.upscale.sort((a, b) => b.createdAt - a.createdAt);
        }
      }
    }

    if (mapModels) {
      dto.models = [];
      if (entity.models?.length) {
        for (const imageCompletionModel of entity.models) {
          if (!imageCompletionModel.modelId) {
            continue;
          }

          try {
            const model =
              imageCompletionModel.model ??
              (await this.modelProvider.get(imageCompletionModel.modelId));

            dto.models.push(
              await this.modelResponseMapper.map(model, true, false),
            );
          } catch (error) {
            this.logger.debug('Error mapping image models', {
              image_completion_id: entity.id,
              image_completion_model_id: imageCompletionModel?.id,
              image_completion_model: imageCompletionModel,
              error: error.message,
            });
          }
        }
      }
    }

    return dto;
  }

  generateThumbnailUrl(imageCompletion: ImageCompletionEntity): string {
    if (!imageCompletion.isResized) {
      return this.cdnHost + '/' + imageCompletion.imagePaths[0];
    }

    const imageVersions = this.generateImageVersions(
      imageCompletion.imagePaths[0],
    );

    return imageVersions['96x96'];
  }

  generateImageVersions(originalImage: string): {
    [key: string]: string;
  } {
    const originalImageUrl = this.cdnHost + '/' + originalImage;

    const versions = {
      original: originalImageUrl,
      '96x96': this.composeUrlWithResolution(originalImageUrl, '96x96'),
      '240x240': this.composeUrlWithResolution(originalImageUrl, '240x240'),
      '640x640': this.composeUrlWithResolution(originalImageUrl, '640x640'),
      '1920x1920': this.composeUrlWithResolution(originalImageUrl, '1920x1920'),
    };

    return versions;
  }

  private composeUrlWithResolution(
    originalImageUrl: string,
    resolution: string,
  ): string {
    const [base, extension] = originalImageUrl.split(/\.(?=[^\.]+$)/);
    return `${base}_${resolution}.${extension}`;
  }

  async mapMultipleInternal(entities: ImageCompletionEntity[]): Promise<any> {
    const images = [];

    for (const entity of entities) {
      images.push(await this.mapInternal(entity));
    }

    return images;
  }

  async mapInternal(
    entity: ImageCompletionEntity,
  ): Promise<ImageCompletionDto> {
    const dto = await this.map(entity, true, false);

    const user = entity.user ?? (await this.userProvider.get(entity.userId));

    dto.baseModel = entity.baseModel;
    dto.userId = user.id;
    dto.promptSystem = entity.promptSystem;
    dto.storageBucket = entity.storageBucket;
    dto.storagePath = entity.storagePath;
    dto.generationSettings = entity.generationSettings;
    dto.generationData = entity.generationData;
    dto.generatedByUnit = entity.generatedByUnit;
    dto.generationSeconds = entity.generationSeconds;

    dto.models = [];
    if (entity.models?.length) {
      for (const imageCompletionModel of entity.models) {
        try {
          dto.models.push(
            await this.modelResponseMapper.mapInternal(
              imageCompletionModel.model,
            ),
          );
        } catch (error) {
          this.logger.debug('Error mapping image models', {
            image_completion_id: entity.id,
            image_completion_model_id: imageCompletionModel?.id,
            image_completion_model: imageCompletionModel,
            error: error.message,
          });
        }
      }
    }

    return dto;
  }
}
