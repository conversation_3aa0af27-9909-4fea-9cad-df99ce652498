import { Controller, Post, Request } from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { UserProvider } from 'src/user/service/provider';
import { ImageCompletionDto } from '../dto/image-completion.dto';
import { ImageCompletionRequest } from '../dto/image-completion.request';
import { ImageCompletionRequestManager } from '../service/request-manager';
import { ImageCompletionResponseMapper } from '../service/response-mapper';

@ApiTags('image_completion')
@Controller('image_completions')
export class CreateController {
  constructor(
    private requestManager: ImageCompletionRequestManager,
    private responseMapper: ImageCompletionResponseMapper,
    private userProvider: UserProvider,
  ) {}

  @Post()
  @ApiOperation({
    operationId: 'image_completion_create',
    summary: 'Create a new image completion',
    description:
      'Creates a new image completion for the authenticated user.\n\n' +
      'Optional Parameters:\n' +
      '- prompt: Text description of the desired image\n' +
      '- imageCompletionId: UUID of the image completion to regenerate\n' +
      '- organizationId: UUID of the organization to deduct credits from\n' +
      '- queue: Queue to use for image completion\n' +
      '- generationSettings: Generation settings for the image completion\n' +
      '- hideFromUserProfile: Hide the image completion from the user profile\n' +
      '- hasWatermark: Apply watermark to the image completion\n' +
      '- webhookUrl: URL to receive a POST notification when image completion is complete\n\n' +
      'Response includes:\n' +
      '- originalImageCompletions: Array of original image completions used as source\n' +
      '- originalImageCompletionIds: Array of UUIDs of original image completions\n' +
      '- systemVersion: AI system version (2 or 3)\n\n' +
      'Returns the created image completion object.',
  })
  @ApiBody({
    type: ImageCompletionRequest,
    description: 'Image completion creation parameters.',
  })
  @ApiOkResponse({
    type: ImageCompletionDto,
    description: 'Image completion created successfully.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:\n
      - \`prompt\`: Must be a non-empty string.
      - \`prompt\`: Model not available.
      - \`imageCompletionId\`: Must be a valid UUID.
      - \`organizationId\`: Must be a valid UUID.
      - \`queue\`: Must be a valid value from the \`QueueEnum\`.
      - \`generationSettings\`: Must be a valid object.
      - \`hideFromUserProfile\`: Must be a valid boolean.
      - \`hasWatermark\`: Must be a valid boolean.
      - \`webhookUrl\`: Must be a valid URL.
      - \`systemVersion\`: Must be a valid system version (2 or 3).
      - Invalid or unavailable parameters.
      - Insufficient credits.
      - Failed to analyze request body.
    `,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized. Authentication token is missing or invalid.',
  })
  @ApiInternalServerErrorResponse({
    description:
      'Internal Server Error. Unexpected error occurred during image completion creation.',
  })
  async create(
    @Body() requestBody: ImageCompletionRequest,
    @Request() request,
  ): Promise<ImageCompletionDto> {
    const user = await this.userProvider.get(request.user.id);

    const imageCompletion = await this.requestManager.create(requestBody, user);

    return this.responseMapper.map(imageCompletion);
  }
}
