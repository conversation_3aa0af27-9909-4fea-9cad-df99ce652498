import { DynamicModule, forwardRef, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from 'src/auth/auth.module';
import { jwtConfig } from 'src/auth/config/jwt.config';
import { ImageCompletionModule } from 'src/image-completion/module';
import { ModelModule } from 'src/model/module';
import { OrganizationModule } from 'src/organization/organization.module';
import { SubscriptionModule } from 'src/subscription/module';
import { UserModule } from 'src/user/user.module';
import { CreateController } from './controller/create.controller';
import { DeleteController } from './controller/delete.controller';
import { ReadController as InternalReadController } from './controller/internal/read.controller';
import { UpdateController as InternalUpdateController } from './controller/internal/update.controller';
import { ReadController } from './controller/read.controller';
import { UpdateController } from './controller/update.controller';
import { ImageEditImageCompletionEntity } from './entity/image-edit-image-completion.entity';
import { ImageEditModelEntity } from './entity/image-edit-model.entity';
import { ImageEditOriginalImageCompletionEntity } from './entity/image-edit-original-image-completion.entity';
import { ImageEditEntity } from './entity/image-edit.entity';
import { ImageEditManager } from './service/manager';
import { ImageEditProvider } from './service/provider';
import { ImageEditRequestManager } from './service/request-manager';
import { ImageEditResponseMapper } from './service/response-mapper';
import { ImageEditImageCompletionProvider } from './service/image-edit-image-completion.provider';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ImageEditEntity,
      ImageEditModelEntity,
      ImageEditImageCompletionEntity,
      ImageEditOriginalImageCompletionEntity,
    ]),
    PassportModule,
    JwtModule.register(jwtConfig),
    forwardRef(() => AuthModule),
    forwardRef(() => ImageCompletionModule),
    forwardRef(() => ModelModule),
    forwardRef(() => OrganizationModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => UserModule),
  ],
  providers: [
    ImageEditProvider,
    ImageEditManager,
    ImageEditImageCompletionProvider,
    ImageEditRequestManager,
    ImageEditResponseMapper,
  ],
  exports: [
    ImageEditProvider,
    ImageEditManager,
    ImageEditRequestManager,
    ImageEditResponseMapper,
    ImageEditImageCompletionProvider,
  ],
})
export class ImageEditModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: ImageEditModule,
      controllers: enableControllers
        ? [
            CreateController,
            DeleteController,
            UpdateController,
            ReadController,
            InternalReadController,
            InternalUpdateController,
          ]
        : [],
    };
  }
}
