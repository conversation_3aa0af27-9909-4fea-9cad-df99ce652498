import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';
import { PaymentRequiredException } from 'src/subscription/exception/payment-required.exception';
import { UserEntity } from '../../user/entity/user.entity';
import { ImageEditInternalRequest } from '../dto/image-edit.internal-request';
import { ImageEditRequest } from '../dto/image-edit.request';
import {
  EditMode,
  ImageEditEntity,
  StatusEnum,
} from '../entity/image-edit.entity';
import { ImageEditOriginalImageCompletionEntity } from '../entity/image-edit-original-image-completion.entity';
import { ImageEditManager } from './manager';
import { ImageEditProvider } from './provider';
import * as sharp from 'sharp';
import axios from 'axios';

@Injectable()
export class ImageEditRequestManager {
  constructor(
    private readonly manager: ImageEditManager,
    private readonly provider: ImageEditProvider,
    private readonly imageCompletionProvider: ImageCompletionProvider,
    private readonly imageEditProvider: ImageEditProvider,
    private readonly organizationUserProvider: OrganizationUserProvider,
    private readonly logger: Logger,
  ) {}

  async create(
    request: ImageEditRequest,
    user: UserEntity,
  ): Promise<ImageEditEntity> {
    const imageEdit = new ImageEditEntity();
    imageEdit.user = user;
    imageEdit.userId = user.id;

    // If mode is 'skin', always set imageCompletionsCount to 1
    imageEdit.imageCompletionsCount =
      request.mode === EditMode.SKIN ? 1 : request.imageCompletionsCount;

    imageEdit.inputImageUrls = request.inputImageUrls;

    // Create relationship entities for original image completions
    if (
      request.originalImageCompletionIds &&
      request.originalImageCompletionIds.length > 0
    ) {
      imageEdit.originalImageCompletionRelations =
        request.originalImageCompletionIds.map((imageCompletionId) => {
          const relation = new ImageEditOriginalImageCompletionEntity();
          relation.imageCompletionId = imageCompletionId;
          return relation;
        });
    } else {
      imageEdit.originalImageCompletionRelations = [];
    }

    if (
      request.originalImageCompletionIds &&
      request.originalImageCompletionIds.length > 0
    ) {
      // For now, we'll use the first originalImageCompletionId for backward compatibility
      // In the future, this could be enhanced to handle multiple original images
      const originalImageCompletion = await this.imageCompletionProvider.get(
        request.originalImageCompletionIds[0],
      );

      // Note: originalImageCompletion property removed, using originalImageCompletionIds array instead

      if (originalImageCompletion.imageEditImageCompletion) {
        try {
          const originalImageEdit =
            originalImageCompletion.imageEditImageCompletion.imageEdit ??
            (await this.imageEditProvider.get(
              originalImageCompletion.imageEditImageCompletion.imageEditId,
            ));

          // If no input image URLs provided, use the original image edit's URLs
          if (
            !imageEdit.inputImageUrls ||
            imageEdit.inputImageUrls.length === 0
          ) {
            imageEdit.inputImageUrls = originalImageEdit.inputImageUrls;
          }
        } catch (error) {
          this.logger.debug(
            'Error fetching the edit of the original image completion',
            {
              image_completion: originalImageCompletion.id,
              error: error.message,
            },
          );
        }
      }
    }

    if (user.hidePrompt) {
      imageEdit.hidePrompt = user.hidePrompt;
    }

    // Map request data to entity
    this.mapRequestData(imageEdit, request);

    // If width or height is missing, get from original image
    if (!imageEdit.width || !imageEdit.height) {
      try {
        // First, check if we have originalImageCompletionRelations
        if (
          imageEdit.originalImageCompletionRelations &&
          imageEdit.originalImageCompletionRelations.length > 0
        ) {
          // Get the first original image completion for dimensions
          const originalImageCompletion =
            await this.imageCompletionProvider.get(
              imageEdit.originalImageCompletionRelations[0].imageCompletionId,
            );

          // Try to get dimensions from originalImageCompletion's generationSettings
          if (originalImageCompletion.generationSettings) {
            const settings = originalImageCompletion.generationSettings;

            if (settings.width && !imageEdit.width) {
              imageEdit.width = settings.width;
              this.logger.debug('Using width from originalImageCompletion', {
                width: imageEdit.width,
                imageCompletionId: originalImageCompletion.id,
              });
            }

            if (settings.height && !imageEdit.height) {
              imageEdit.height = settings.height;
              this.logger.debug('Using height from originalImageCompletion', {
                height: imageEdit.height,
                imageCompletionId: originalImageCompletion.id,
              });
            }
          }
        }

        // If we still don't have dimensions, fall back to getting them from inputImageUrls
        if (!imageEdit.width || !imageEdit.height) {
          const imageUrl =
            imageEdit.inputImageUrls && imageEdit.inputImageUrls.length > 0
              ? imageEdit.inputImageUrls[0]
              : null;

          // Get image dimensions using Sharp
          if (imageUrl) {
            const response = await axios.get(imageUrl, {
              responseType: 'arraybuffer',
            });
            const metadata = await sharp(response.data).metadata();

            if (metadata.width && metadata.height) {
              if (!imageEdit.width) {
                imageEdit.width = metadata.width;
                this.logger.debug('Using width from inputImageUrl', {
                  width: imageEdit.width,
                });
              }

              if (!imageEdit.height) {
                imageEdit.height = metadata.height;
                this.logger.debug('Using height from inputImageUrl', {
                  height: imageEdit.height,
                });
              }
            }
          }
        }
      } catch (error) {
        this.logger.error('Error getting image dimensions', {
          error: error.message,
          inputImageUrls: imageEdit.inputImageUrls,
        });
      }
    }

    if (request.organizationId) {
      if (
        !(await this.organizationUserProvider.isMember(
          user.id,
          request.organizationId,
        ))
      ) {
        throw new UnauthorizedException();
      }

      imageEdit.organizationId = request.organizationId;
    }

    return await this.manager.create(imageEdit);
  }

  async update(
    id: string,
    request: ImageEditRequest,
    user: UserEntity,
  ): Promise<ImageEditEntity> {
    const imageEdit = await this.provider.getBy({
      id: id,
      userId: user.id,
    });
    this.mapRequestData(imageEdit, request);

    return await this.manager.update(imageEdit);
  }

  async saveImageCompletion(
    id: string,
    imageCompletionId: string,
    user: UserEntity,
  ): Promise<ImageEditEntity> {
    const imageEdit = await this.provider.getBy({
      id,
      userId: user.id,
    });

    if (!imageEdit.hasImageCompletion(imageCompletionId)) {
      throw new NotFoundException('Image completion not found');
    }

    const imageCompletion = await this.imageCompletionProvider.get(
      imageCompletionId,
    );

    return await this.manager.saveImageCompletion(imageEdit, imageCompletion);
  }

  async delete(id: string, user: UserEntity): Promise<void> {
    const imageEdit = await this.provider.getBy({
      id,
      userId: user.id,
    });

    await this.manager.delete(imageEdit);
  }

  mapRequestData(entity: ImageEditEntity, request: ImageEditRequest): void {
    entity.mode = 'mode' in request ? request.mode : entity.mode;
    entity.width = 'width' in request ? request.width : entity.width;
    entity.height = 'height' in request ? request.height : entity.height;
    entity.prompt = 'prompt' in request ? request.prompt : entity.prompt;
    entity.mask = 'mask' in request ? request.mask : entity.mask;
    entity.webhookUrl =
      'webhookUrl' in request ? request.webhookUrl : entity.webhookUrl;
    entity.settings =
      'settings' in request ? request.settings : entity.settings;
    entity.hidePrompt =
      'hidePrompt' in request ? request.hidePrompt : entity.hidePrompt;
  }

  async updateInternal(
    id: string,
    request: ImageEditInternalRequest,
  ): Promise<ImageEditEntity> {
    const imageEdit = await this.provider.get(id);

    imageEdit.status = 'status' in request ? request.status : imageEdit.status;
    imageEdit.generationSeconds =
      'generationSeconds' in request
        ? request.generationSeconds
        : imageEdit.generationSeconds;
    imageEdit.width = 'width' in request ? request.width : imageEdit.width;
    imageEdit.height = 'height' in request ? request.height : imageEdit.height;

    return await this.manager.update(imageEdit);
  }

  async startGeneration(id: string): Promise<void> {
    const entity = await this.provider.get(id);

    if (entity.status != StatusEnum.NEW) {
      throw new BadRequestException('Image edit is not new');
    }

    await this.manager.startGeneration(entity);
  }

  async finishGeneration(
    id: string,
    request: ImageEditInternalRequest,
  ): Promise<void> {
    const entity = await this.provider.get(id);

    if (entity.status != StatusEnum.GENERATING) {
      throw new BadRequestException('Image edit is not being generated');
    }

    entity.generationSeconds =
      'generationSeconds' in request
        ? request.generationSeconds
        : entity.generationSeconds;

    await this.manager.finishGeneration(entity);
  }

  async failGeneration(id: string): Promise<void> {
    const entity = await this.provider.get(id);

    if (entity.status != StatusEnum.GENERATING) {
      throw new BadRequestException('Image edit is not being generated');
    }

    await this.manager.failGeneration(entity);
  }

  async interrupt(entity: ImageEditEntity): Promise<void> {
    if (
      entity.status != StatusEnum.GENERATING &&
      entity.status != StatusEnum.NEW
    ) {
      throw new BadRequestException('Image edit is not being generated');
    }

    try {
      await this.manager.interrupt(entity);
    } catch (e) {
      if ('user_credit_balance.not_found' === e.message) {
        throw new PaymentRequiredException();
      }

      this.logger.error('Error interrupting image generation', {
        imageCompletion: entity,
        error: e.message,
      });

      throw new BadRequestException(e.message);
    }
  }
}
