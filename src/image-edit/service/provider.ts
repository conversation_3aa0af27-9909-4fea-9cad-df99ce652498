import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { EntityNotFoundError, Repository, SelectQueryBuilder } from 'typeorm';
import { ImageEditEntity } from '../entity/image-edit.entity';

@Injectable()
export class ImageEditProvider extends AbstractProvider<ImageEditEntity> {
  constructor(
    @InjectRepository(ImageEditEntity)
    repository: Repository<ImageEditEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  async get(id: string): Promise<ImageEditEntity> {
    const imageEdit = await this.createBaseQueryBuilder()
      .where('imageEdit.id = :id', { id })
      .getOne();

    if (!imageEdit) {
      throw new EntityNotFoundError(this.repository.target, id);
    }

    return imageEdit;
  }

  async findBy(
    criteria: any,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'ASC',
  ): Promise<ImageEditEntity[]> {
    const queryBuilder = this.prepareQueryBuilder(criteria);

    queryBuilder
      .offset((page - 1) * limit)
      .limit(limit)
      .orderBy(`imageEdit.${sortBy}`, sortOrder);

    return await queryBuilder.getMany();
  }

  prepareQueryBuilder(criteria: any): SelectQueryBuilder<ImageEditEntity> {
    const where = { ...criteria };
    const queryBuilder = this.createBaseQueryBuilder();

    Object.keys(where).forEach((key) => {
      queryBuilder.andWhere(`imageEdit.${key} = :${key}`, {
        [key]: where[key],
      });
    });

    return queryBuilder;
  }

  createBaseQueryBuilder(): SelectQueryBuilder<ImageEditEntity> {
    return this.repository
      .createQueryBuilder('imageEdit')
      .innerJoinAndSelect('imageEdit.user', 'user')
      .leftJoinAndSelect(
        'imageEdit.generatedImageCompletion',
        'generatedImageCompletion',
      )
      .leftJoinAndSelect(
        'generatedImageCompletion.user',
        'generatedImageCompletionUser',
      )
      .leftJoinAndSelect('imageEdit.imageCompletions', 'imageCompletions')
      .leftJoinAndSelect(
        'imageCompletions.imageCompletion',
        'imageCompletionChoice',
      )
      .leftJoinAndSelect(
        'imageCompletionChoice.user',
        'imageCompletionChoiceUser',
      )
      .leftJoinAndSelect(
        'imageCompletionChoice.models',
        'imageCompletionChoiceModels',
      )
      .leftJoinAndSelect(
        'imageCompletionChoiceModels.model',
        'imageCompletionChoiceModel',
      )
      .leftJoinAndSelect('imageEdit.models', 'imageEditModels')
      .leftJoinAndSelect('imageEditModels.model', 'model')
      .leftJoinAndSelect(
        'imageEdit.originalImageCompletionRelations',
        'originalImageCompletionRelations',
      );
  }
}
