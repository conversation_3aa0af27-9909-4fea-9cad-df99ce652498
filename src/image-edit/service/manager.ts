import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import {
  ImageCompletionEntity,
  PrivacyEnum as ImageCompletionPrivacyEnum,
  StatusEnum as ImageCompletionStatusEnum,
  QueueEnum,
} from 'src/image-completion/entity/image-completion.entity';
import { ImageCompletionManager } from 'src/image-completion/service/manager';
import { PromptManager } from 'src/image-completion/service/prompt.manager';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { CreditTypeEnum } from 'src/subscription/entity/credit-type.enum';
import { TransactionTypeEnum } from 'src/subscription/entity/transaction-type.enum';
import { TransactionManager } from 'src/subscription/service/transaction.manager';
import { UserProvider } from 'src/user/service/provider';
import { Repository } from 'typeorm';
import { ImageEditImageCompletionEntity } from '../entity/image-edit-image-completion.entity';
import { ImageEditModelEntity } from '../entity/image-edit-model.entity';
import { ImageCompletionOriginalImageCompletionEntity } from 'src/image-completion/entity/image-completion-original-image-completion.entity';
import { ImageEditEntity, StatusEnum } from '../entity/image-edit.entity';
import { ImageEditResponseMapper } from './response-mapper';
import axios from 'axios';
import { Logger } from 'nestjs-pino';

@Injectable()
export class ImageEditManager {
  private CREDITS_SUCCESS_PRICE = {
    in: 20,
    out: 20,
    skin: 10,
    context: 30,
  };

  private CREDITS_INTERRUPTION_PRICE = 1;

  constructor(
    @InjectRepository(ImageEditEntity)
    private repository: Repository<ImageEditEntity>,
    @InjectRepository(ImageEditImageCompletionEntity)
    private imageEditImageCompletionRepository: Repository<ImageEditImageCompletionEntity>,
    @InjectRepository(ImageEditModelEntity)
    private imageEditModelRepository: Repository<ImageEditModelEntity>,
    private responseMapper: ImageEditResponseMapper,
    private imageCompletionManager: ImageCompletionManager,
    @Inject('SQS')
    private sqs: SQSClient,
    private configService: ConfigService,
    private promptManager: PromptManager,
    private transactionManager: TransactionManager,
    private userProvider: UserProvider,
    private imageCompletionProvider: ImageCompletionProvider,
    private logger: Logger,
  ) {}

  async create(entity: ImageEditEntity): Promise<ImageEditEntity> {
    await this.save(entity);

    if (entity.prompt) {
      await this.generateImageEditModels(entity);
    }

    await this.generateImageCompletions(entity);

    await this.writeToQueue(entity);

    return entity;
  }

  async generateImageEditModels(entity: ImageEditEntity) {
    const models = await this.promptManager.extractModelsFromPrompt(
      entity.prompt,
      entity.user,
    );

    const imageEditModels = [];

    for (const model of models) {
      const imageEditModel = new ImageEditModelEntity();
      imageEditModel.model = model;
      imageEditModel.modelId = model.id;
      imageEditModel.imageEdit = entity;
      imageEditModel.imageEditId = entity.id;

      await this.imageEditModelRepository.save(imageEditModel);

      imageEditModels.push(imageEditModel);
    }

    entity.promptSystem = await this.promptManager.generatePromptSystem(
      entity.prompt,
      models,
      entity.systemVersion,
    );

    await this.save(entity);

    entity.models = imageEditModels;
  }

  async startGeneration(imageEdit: ImageEditEntity) {
    imageEdit.status = StatusEnum.GENERATING;

    await this.save(imageEdit);

    for (const imageEditImageCompletion of imageEdit.imageCompletions) {
      const imageCompletion =
        imageEditImageCompletion.imageCompletion ??
        (await this.imageCompletionProvider.get(
          imageEditImageCompletion.imageCompletionId,
        ));

      imageCompletion.status = ImageCompletionStatusEnum.GENERATING;

      await this.imageCompletionManager.save(imageCompletion);
    }
  }

  async finishGeneration(imageEdit: ImageEditEntity) {
    try {
      imageEdit.status = StatusEnum.READY;

      let creditPrice = this.CREDITS_SUCCESS_PRICE[imageEdit.mode];

      if (imageEdit.generationSeconds) {
        creditPrice = imageEdit.generationSeconds;
      }

      await this.save(imageEdit);

      const user =
        imageEdit.user ?? (await this.userProvider.get(imageEdit.userId));

      if (!user.isBot) {
        await this.registerTransaction(imageEdit, creditPrice);
      }

      if (imageEdit.webhookUrl) {
        try {
          const imageEditDto = await this.responseMapper.map(imageEdit);
          await axios.post(imageEdit.webhookUrl, imageEditDto, {
            headers: {
              'Content-Type': 'application/json',
            },
            timeout: 5000, // 5 second timeout
          });

          this.logger.log('Webhook notification sent successfully', {
            imageEditId: imageEdit.id,
            webhookUrl: imageEdit.webhookUrl,
          });
        } catch (error) {
          this.logger.error('Failed to send webhook notification', {
            imageEditId: imageEdit.id,
            webhookUrl: imageEdit.webhookUrl,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    } catch (e) {
      imageEdit.status = StatusEnum.FAILED;

      await this.save(imageEdit);

      throw e;
    }
  }

  async failGeneration(imageEdit: ImageEditEntity) {
    imageEdit.status = StatusEnum.FAILED;

    await this.save(imageEdit);
  }

  async update(entity: ImageEditEntity): Promise<ImageEditEntity> {
    return await this.save(entity);
  }

  async delete(entity: ImageEditEntity): Promise<void> {
    await this.repository.softDelete(entity.id);
  }

  async save(entity: ImageEditEntity): Promise<ImageEditEntity> {
    return this.repository.save(entity);
  }

  async interrupt(entity: ImageEditEntity): Promise<void> {
    entity.status = StatusEnum.INTERRUPTED;

    await this.save(entity);

    for (const imageEditImageCompletion of entity.imageCompletions) {
      const imageCompletion =
        imageEditImageCompletion.imageCompletion ??
        (await this.imageCompletionProvider.get(
          imageEditImageCompletion.imageCompletionId,
        ));

      imageCompletion.status = ImageCompletionStatusEnum.INTERRUPTED;

      await this.imageCompletionManager.save(imageCompletion);
    }

    const user = entity.user ?? (await this.userProvider.get(entity.userId));

    if (!user.isBot) {
      try {
        await this.registerTransaction(entity, this.CREDITS_INTERRUPTION_PRICE);
      } catch (e) {
        entity.status = StatusEnum.FAILED;
        await this.save(entity);

        throw e;
      }
    }
  }

  async writeToQueue(entity: ImageEditEntity) {
    const queueUrl = this.configService.get<string>('IMAGE_EDIT_SQS_QUEUE_URL');

    const sendMessageCommand = new SendMessageCommand({
      QueueUrl: queueUrl,
      MessageBody: JSON.stringify({
        id: entity.id,
      }),
    });

    await this.sqs.send(sendMessageCommand);
  }

  async generateImageCompletions(entity: ImageEditEntity) {
    let originalImageCompletion = null;

    if (
      entity.originalImageCompletionRelations &&
      entity.originalImageCompletionRelations.length > 0
    ) {
      // For now, we'll use the first originalImageCompletionId for backward compatibility
      // In the future, this could be enhanced to handle multiple original images
      originalImageCompletion = await this.imageCompletionProvider.get(
        entity.originalImageCompletionRelations[0].imageCompletionId,
      );
    }

    const user = entity.user ?? (await this.userProvider.get(entity.userId));

    entity.imageCompletions = [];

    for (let i = 0; i < entity.imageCompletionsCount; i++) {
      const imageCompletion = new ImageCompletionEntity();
      imageCompletion.prompt = entity.prompt;
      imageCompletion.promptSystem = entity.promptSystem;
      imageCompletion.generationSettings =
        originalImageCompletion?.generationSettings ?? {};
      imageCompletion.hasWatermark =
        originalImageCompletion?.hasWatermark ?? true;
      imageCompletion.systemVersion =
        originalImageCompletion?.systemVersion ?? user.systemVersion;
      imageCompletion.user = entity.user;
      imageCompletion.userId = entity.userId;
      imageCompletion.status = ImageCompletionStatusEnum.NEW;
      imageCompletion.privacy = ImageCompletionPrivacyEnum.PRIVATE;
      imageCompletion.queue = QueueEnum.SLOW;
      imageCompletion.isBilled = false;
      // Create relationship to original image completion if it exists
      if (originalImageCompletion?.id) {
        const relation = new ImageCompletionOriginalImageCompletionEntity();
        relation.originalImageCompletionId = originalImageCompletion.id;
        imageCompletion.originalImageCompletionRelations = [relation];
      } else {
        imageCompletion.originalImageCompletionRelations = [];
      }

      await this.imageCompletionManager.generateImageCompletionModels(
        imageCompletion,
      );
      await this.imageCompletionManager.save(imageCompletion);

      const imageEditImageCompletion = new ImageEditImageCompletionEntity();
      imageEditImageCompletion.imageEdit = entity;
      imageEditImageCompletion.imageEditId = entity.id;
      imageEditImageCompletion.imageCompletion = imageCompletion;
      imageEditImageCompletion.imageCompletionId = imageCompletion.id;

      await this.imageEditImageCompletionRepository.save(
        imageEditImageCompletion,
      );

      entity.imageCompletions.push(imageEditImageCompletion);
    }
  }

  async saveImageCompletion(
    imageEdit: ImageEditEntity,
    imageCompletion: ImageCompletionEntity,
  ) {
    imageCompletion.status = ImageCompletionStatusEnum.READY;
    await this.imageCompletionManager.save(imageCompletion);

    imageEdit.generatedImageCompletion = imageCompletion;
    imageEdit.generatedImageCompletionId = imageCompletion.id;
    imageEdit.status = StatusEnum.SAVED;

    return await this.update(imageEdit);
  }

  async registerTransaction(entity: ImageEditEntity, price: number) {
    await this.transactionManager.register(
      TransactionTypeEnum.SPENDING,
      price,
      CreditTypeEnum.IMAGE,
      entity.id,
      true,
      entity.organizationId ? null : entity.userId,
      entity.organizationId,
    );
  }
}
