import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  Join<PERSON><PERSON>umn,
  CreateDateColumn,
  Index,
} from 'typeorm';
import { ImageEditEntity } from './image-edit.entity';
import { ImageCompletionEntity } from '../../image-completion/entity/image-completion.entity';

/**
 * Junction table for the many-to-many relationship between ImageEdit and ImageCompletion
 * This represents the original image completions used as source for an image edit
 */
@Entity('image_edit_original_image_completion')
@Index(['imageEditId', 'imageCompletionId'], { unique: true })
@Index(['imageEditId'])
@Index(['imageCompletionId'])
export class ImageEditOriginalImageCompletionEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(
    () => ImageEditEntity,
    (imageEdit) => imageEdit.originalImageCompletionRelations,
    {
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn({ name: 'image_edit_id' })
  imageEdit: ImageEditEntity;

  @Column({ name: 'image_edit_id', type: 'uuid' })
  imageEditId: string;

  @ManyToOne(() => ImageCompletionEntity, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'image_completion_id' })
  imageCompletion: ImageCompletionEntity;

  @Column({ name: 'image_completion_id', type: 'uuid' })
  imageCompletionId: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}
