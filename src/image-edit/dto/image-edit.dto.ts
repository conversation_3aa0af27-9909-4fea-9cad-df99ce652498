import { ApiProperty } from '@nestjs/swagger';
import { ImageCompletionDto } from 'src/image-completion/dto/image-completion.dto';
import { ModelDto } from 'src/model/dto/model.dto';

export class ImageEditDto {
  @ApiProperty({ description: 'Unique identifier for the image edit' })
  id: string;

  @ApiProperty({
    description: 'Original image completions',
    type: () => [ImageCompletionDto],
  })
  originalImageCompletions?: ImageCompletionDto[];

  @ApiProperty({ description: 'Original image completion IDs', type: [String] })
  originalImageCompletionIds?: string[];

  @ApiProperty({ description: 'Source image completion of the edit' })
  originalImageEdit: ImageEditDto;

  @ApiProperty({
    description: 'Generated image completion',
    type: () => ImageCompletionDto,
  })
  generatedImageCompletion: ImageCompletionDto;

  @ApiProperty({
    description: 'Generated image completion choices',
    type: () => Array<ImageCompletionDto>,
  })
  imageCompletionChoices: ImageCompletionDto[];

  @ApiProperty({
    description: 'Edit mode, either "in", "out", "skin", or "context"',
  })
  mode: string;

  @ApiProperty({ description: 'Width of the edited image' })
  width?: number;

  @ApiProperty({ description: 'Height of the edited image' })
  height?: number;

  @ApiProperty({ description: 'Number of image completions to be generated' })
  imageCompletionsCount: number;

  @ApiProperty({ description: 'Prompt' })
  prompt?: string;

  @ApiProperty({ description: 'Inpainting mask' })
  mask?: string;

  @ApiProperty({ description: 'Input image URLs', type: [String] })
  inputImageUrls?: string[];

  @ApiProperty()
  settings?: any;

  @ApiProperty({ type: () => [ModelDto] })
  models?: ModelDto[];

  @ApiProperty({
    description: 'Status of the image edit process',
    required: false,
  })
  status: string;

  @ApiProperty()
  webhookUrl?: string;

  @ApiProperty()
  hidePrompt?: boolean;
}
