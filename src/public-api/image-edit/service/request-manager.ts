import { Injectable, UnauthorizedException } from '@nestjs/common';
import axios from 'axios';
import { Logger } from 'nestjs-pino';
import * as sharp from 'sharp';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';
import {
  EditMode,
  ImageEditEntity,
} from '../../../image-edit/entity/image-edit.entity';
import { ImageEditOriginalImageCompletionEntity } from '../../../image-edit/entity/image-edit-original-image-completion.entity';
import { ImageEditManager } from '../../../image-edit/service/manager';
import { UserEntity } from '../../../user/entity/user.entity';
import { ImageEditRequest } from '../dto/image-edit.request';

@Injectable()
export class ImageEditRequestManager {
  constructor(
    private readonly manager: ImageEditManager,
    private readonly imageCompletionProvider: ImageCompletionProvider,
    private readonly organizationUserProvider: OrganizationUserProvider,
    private readonly logger: Logger,
  ) {}

  async create(
    request: ImageEditRequest,
    user: UserEntity,
  ): Promise<ImageEditEntity> {
    // Validate organization membership if organizationId is provided
    if (request.organizationId) {
      const isMember = await this.organizationUserProvider.isMember(
        user.id,
        request.organizationId,
      );
      if (!isMember) {
        throw new UnauthorizedException(
          'User is not a member of the specified organization',
        );
      }
    }

    const imageEdit = new ImageEditEntity();
    imageEdit.user = user;
    imageEdit.userId = user.id;

    // If mode is 'skin', always set imageCompletionsCount to 1
    imageEdit.imageCompletionsCount =
      request.mode === EditMode.SKIN ? 1 : request.imageCompletionsCount;

    imageEdit.inputImageUrls = request.imageUrls;

    // Create relationship entities for original image completions
    if (
      request.originalImageCompletionIds &&
      request.originalImageCompletionIds.length > 0
    ) {
      imageEdit.originalImageCompletionRelations =
        request.originalImageCompletionIds.map((imageCompletionId) => {
          const relation = new ImageEditOriginalImageCompletionEntity();
          relation.imageCompletionId = imageCompletionId;
          return relation;
        });
    } else {
      imageEdit.originalImageCompletionRelations = [];
    }

    if (user.hidePrompt) {
      imageEdit.hidePrompt = user.hidePrompt;
    }

    // Map request data to entity
    this.mapRequestData(imageEdit, request);

    // Set organizationId if provided for organization credit deduction
    if (request.organizationId) {
      imageEdit.organizationId = request.organizationId;
    }

    // If width or height is missing, get from original image
    if (!imageEdit.width || !imageEdit.height) {
      try {
        // First, check if we have originalImageCompletionRelations
        if (
          imageEdit.originalImageCompletionRelations &&
          imageEdit.originalImageCompletionRelations.length > 0
        ) {
          // Get the first original image completion for dimensions
          const originalImageCompletion =
            await this.imageCompletionProvider.get(
              imageEdit.originalImageCompletionRelations[0].imageCompletionId,
            );

          // Try to get dimensions from originalImageCompletion's generationSettings
          if (originalImageCompletion.generationSettings) {
            const settings = originalImageCompletion.generationSettings;

            if (settings.width && !imageEdit.width) {
              imageEdit.width = settings.width;
              this.logger.debug('Using width from originalImageCompletion', {
                width: imageEdit.width,
                imageCompletionId: originalImageCompletion.id,
              });
            }

            if (settings.height && !imageEdit.height) {
              imageEdit.height = settings.height;
              this.logger.debug('Using height from originalImageCompletion', {
                height: imageEdit.height,
                imageCompletionId: originalImageCompletion.id,
              });
            }
          }
        }

        // If we still don't have dimensions, fall back to getting them from inputImageUrls
        if (!imageEdit.width || !imageEdit.height) {
          const imageUrl =
            imageEdit.inputImageUrls && imageEdit.inputImageUrls.length > 0
              ? imageEdit.inputImageUrls[0]
              : null;

          // Get image dimensions using Sharp
          if (imageUrl) {
            const response = await axios.get(imageUrl, {
              responseType: 'arraybuffer',
            });
            const buffer = Buffer.from(response.data); // <-- Ensure Buffer
            const metadata = await sharp(buffer).metadata();

            if (metadata.width && metadata.height) {
              if (!imageEdit.width) {
                imageEdit.width = metadata.width;
                this.logger.debug('Using width from inputImageUrl', {
                  width: imageEdit.width,
                });
              }

              if (!imageEdit.height) {
                imageEdit.height = metadata.height;
                this.logger.debug('Using height from inputImageUrl', {
                  height: imageEdit.height,
                });
              }
            } else {
              this.logger.warn('Image metadata missing width/height', {
                metadata,
              });
            }
          }
        }
      } catch (error) {
        this.logger.error('Error getting image dimensions', {
          error: error.message,
          inputImageUrls: imageEdit.inputImageUrls,
        });
      }
    }

    return await this.manager.create(imageEdit);
  }

  mapRequestData(entity: ImageEditEntity, request: ImageEditRequest): void {
    entity.mode = 'mode' in request ? request.mode : entity.mode;
    entity.width = 'width' in request ? request.width : entity.width;
    entity.height = 'height' in request ? request.height : entity.height;
    entity.prompt = 'prompt' in request ? request.prompt : entity.prompt;
    entity.mask = 'mask' in request ? request.mask : entity.mask;
    entity.webhookUrl =
      'webhookUrl' in request ? request.webhookUrl : entity.webhookUrl;
    entity.settings =
      'settings' in request ? request.settings : entity.settings;
  }
}
