import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DateTime } from 'luxon';
import { Logger } from 'nestjs-pino';
import { Notifier } from 'src/notification/service/notifier';
import { UserManager } from 'src/user/service/manager';
import { UserProvider } from 'src/user/service/provider';
import { Repository } from 'typeorm';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { SubscriptionEntity } from '../entity/subscription.entity';
import { TransactionTypeEnum } from '../entity/transaction-type.enum';
import { CreditPackageProvider } from './credit-package.provider';
import { EmailManager } from './email.manager';
import { SubscriptionManager } from './manager';
import { SubscriptionProvider } from './provider';
import { StripeService } from './stripe.service';
import { TransactionManager } from './transaction.manager';
import { UserCreditBalanceManager } from './user-credit-balance.manager';

describe('SubscriptionManager - Yearly Subscriptions', () => {
  let manager: SubscriptionManager;
  let repository: Repository<SubscriptionEntity>;
  let stripeService: StripeService;
  let userCreditBalanceManager: UserCreditBalanceManager;
  let transactionManager: TransactionManager;
  let emailManager: EmailManager;
  let notifier: Notifier;
  let logger: Logger;

  const mockRepository = {
    save: jest.fn(),
    softDelete: jest.fn(),
  };

  const mockStripeService = {
    getOrCreateStripePriceId: jest.fn(),
    calculateProration: jest.fn(),
    upgradeToYearlySubscription: jest.fn(),
  };

  const mockUserCreditBalanceManager = {
    increase: jest.fn(),
  };

  const mockTransactionManager = {
    register: jest.fn(),
  };

  const mockEmailManager = {
    sendRenewedEmail: jest.fn(),
  };

  const mockNotifier = {
    dispatch: jest.fn(),
  };

  const mockLogger = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  };

  const mockUserProvider = {
    get: jest.fn().mockResolvedValue({
      id: 'user_123',
      email: '<EMAIL>',
      username: 'testuser',
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SubscriptionManager,
        {
          provide: getRepositoryToken(SubscriptionEntity),
          useValue: mockRepository,
        },
        {
          provide: SubscriptionProvider,
          useValue: {},
        },
        {
          provide: 'Stripe',
          useValue: {},
        },
        {
          provide: UserProvider,
          useValue: mockUserProvider,
        },
        {
          provide: UserManager,
          useValue: {},
        },
        {
          provide: CreditPackageProvider,
          useValue: {},
        },
        {
          provide: StripeService,
          useValue: mockStripeService,
        },
        {
          provide: TransactionManager,
          useValue: mockTransactionManager,
        },
        {
          provide: ConfigService,
          useValue: {},
        },
        {
          provide: UserCreditBalanceManager,
          useValue: mockUserCreditBalanceManager,
        },
        {
          provide: EmailManager,
          useValue: mockEmailManager,
        },
        {
          provide: EventEmitter2,
          useValue: {},
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
        {
          provide: Notifier,
          useValue: mockNotifier,
        },
      ],
    }).compile();

    manager = module.get<SubscriptionManager>(SubscriptionManager);
    repository = module.get<Repository<SubscriptionEntity>>(
      getRepositoryToken(SubscriptionEntity),
    );
    stripeService = module.get<StripeService>(StripeService);
    userCreditBalanceManager = module.get<UserCreditBalanceManager>(
      UserCreditBalanceManager,
    );
    transactionManager = module.get<TransactionManager>(TransactionManager);
    emailManager = module.get<EmailManager>(EmailManager);
    notifier = module.get<Notifier>(Notifier);
    logger = module.get<Logger>(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('calculateCreditExpirationDate', () => {
    it('should return monthly expiration plus one hour for yearly subscriptions', () => {
      const yearlySubscription = {
        creditPackage: { expiresAfterMonths: 12 },
        paidAt: new Date('2024-01-01'),
        renewedAt: null,
        createdAt: new Date('2024-01-01'),
      } as SubscriptionEntity;

      const result =
        manager['calculateCreditExpirationDate'](yearlySubscription);
      const expected = DateTime.fromJSDate(new Date('2024-01-01')).plus({
        months: 1,
        hours: 1,
      });

      expect(result.toISO()).toBe(expected.toISO());
    });

    it('should return subscription expiration plus one hour for monthly subscriptions', () => {
      const monthlySubscription = {
        creditPackage: { expiresAfterMonths: 1 },
        getExpiresAt: () => DateTime.fromJSDate(new Date('2024-02-01')),
      } as SubscriptionEntity;

      const result =
        manager['calculateCreditExpirationDate'](monthlySubscription);
      const expected = DateTime.fromJSDate(new Date('2024-02-01')).plus({
        hours: 1,
      });

      expect(result.toISO()).toBe(expected.toISO());
    });

    it('should use renewedAt date when available for yearly subscriptions', () => {
      const yearlySubscription = {
        creditPackage: { expiresAfterMonths: 12 },
        paidAt: new Date('2024-01-01'),
        renewedAt: new Date('2024-02-01'),
        createdAt: new Date('2024-01-01'),
      } as SubscriptionEntity;

      const result =
        manager['calculateCreditExpirationDate'](yearlySubscription);
      const expected = DateTime.fromJSDate(new Date('2024-02-01')).plus({
        months: 1,
        hours: 1,
      });

      expect(result.toISO()).toBe(expected.toISO());
    });
  });

  describe('handleYearlySubscriptionRenewal', () => {
    it('should handle yearly subscription renewal correctly', async () => {
      const subscription = {
        id: 'sub_123',
        userId: 'user_123',
        creditPackageId: 'pkg_123',
        creditPackage: {
          id: 'pkg_123',
          name: 'Pro - Yearly',
          price: 7490,
          expiresAfterMonths: 12,
        },
        renewedAt: new Date('2024-01-01'),
        stripeLatestInvoice: 'inv_123',
      } as SubscriptionEntity;

      await manager.handleYearlySubscriptionRenewal(subscription);

      expect(mockTransactionManager.register).toHaveBeenCalledWith(
        TransactionTypeEnum.SUBSCRIPTION,
        7490,
        'Pro - Yearly',
        'sub_123|2024-01-01T00:00:00.000Z',
        false,
        'user_123',
        null,
        expect.objectContaining({
          subscriptionId: 'sub_123',
          userId: 'user_123',
          creditPackageId: 'pkg_123',
          stripeInvoice: 'inv_123',
        }),
      );

      expect(mockEmailManager.sendRenewedEmail).toHaveBeenCalledWith(
        subscription,
      );
      expect(mockNotifier.dispatch).toHaveBeenCalled();
      expect(mockLogger.log).toHaveBeenCalledWith(
        'subscription.yearly_renewal',
        expect.any(Object),
      );
    });
  });

  describe('isYearlySubscription', () => {
    it('should return true for yearly subscriptions', () => {
      const yearlySubscription = {
        creditPackage: { expiresAfterMonths: 12 },
      } as SubscriptionEntity;

      const result = manager.isYearlySubscription(yearlySubscription);

      expect(result).toBe(true);
    });

    it('should return false for monthly subscriptions', () => {
      const monthlySubscription = {
        creditPackage: { expiresAfterMonths: 1 },
      } as SubscriptionEntity;

      const result = manager.isYearlySubscription(monthlySubscription);

      expect(result).toBe(false);
    });

    it('should return false for other subscription types', () => {
      const otherSubscription = {
        creditPackage: { expiresAfterMonths: 3 },
      } as SubscriptionEntity;

      const result = manager.isYearlySubscription(otherSubscription);

      expect(result).toBe(false);
    });
  });

  describe('registerTopUpTransaction - Yearly Subscriptions', () => {
    it('should use monthly expiration for yearly subscription credits', async () => {
      const yearlySubscription = {
        id: 'sub_123',
        userId: 'user_123',
        creditPackage: {
          id: 'pkg_123',
          name: 'Pro - Yearly',
          price: 7490,
          expiresAfterMonths: 12,
          creditTypes: {
            [CreditTypeEnum.IMAGE]: 36000,
            [CreditTypeEnum.MODEL]: 20,
          },
        },
        paidAt: new Date('2024-01-01'),
        renewedAt: null,
        createdAt: new Date('2024-01-01'),
      } as SubscriptionEntity;

      // Mock the user provider
      const mockUserProvider = {
        get: jest.fn().mockResolvedValue({ id: 'user_123' }),
      };
      (manager as any).userProvider = mockUserProvider;

      await manager.registerTopUpTransaction(yearlySubscription);

      // Verify that credits are allocated with monthly expiration plus one hour
      const expectedExpirationDate = DateTime.fromJSDate(new Date('2024-01-01'))
        .plus({ months: 1, hours: 1 })
        .toJSDate();

      expect(mockUserCreditBalanceManager.increase).toHaveBeenCalledWith(
        CreditTypeEnum.IMAGE,
        36000,
        'user_123',
        null,
        expectedExpirationDate,
      );

      expect(mockUserCreditBalanceManager.increase).toHaveBeenCalledWith(
        CreditTypeEnum.MODEL,
        20,
        'user_123',
        null,
        expectedExpirationDate,
      );
    });
  });
});
